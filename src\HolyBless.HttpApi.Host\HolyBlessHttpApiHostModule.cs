using HolyBless.Configs;
using HolyBless.EntityFrameworkCore;
using HolyBless.MultiTenancy;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Extensions.DependencyInjection;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.HttpOverrides;
using Microsoft.AspNetCore.Localization;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.OpenApi.Models;
using OpenIddict.Server.AspNetCore;
using OpenIddict.Validation.AspNetCore;
using Swashbuckle.AspNetCore.SwaggerGen;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Account;
using Volo.Abp.Account.Web;
using Volo.Abp.AspNetCore.MultiTenancy;
using Volo.Abp.AspNetCore.Mvc;
using Volo.Abp.AspNetCore.Mvc.UI.Bundling;
using Volo.Abp.AspNetCore.Mvc.UI.Theme.Basic;
using Volo.Abp.AspNetCore.Mvc.UI.Theme.Basic.Bundling;
using Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared;
using Volo.Abp.AspNetCore.Serilog;
using Volo.Abp.Autofac;
using Volo.Abp.BackgroundWorkers;
using Volo.Abp.Identity;
using Volo.Abp.Modularity;
using Volo.Abp.OpenIddict;
using Volo.Abp.PermissionManagement.HttpApi;
using Volo.Abp.Security.Claims;
using Volo.Abp.SettingManagement;
using Volo.Abp.Studio.Client.AspNetCore;
using Volo.Abp.Swashbuckle;
using Volo.Abp.Timing;
using Volo.Abp.UI.Navigation.Urls;
using Volo.Abp.VirtualFileSystem;

namespace HolyBless;

[DependsOn(
    typeof(HolyBlessHttpApiModule),
    typeof(AbpStudioClientAspNetCoreModule),
    typeof(AbpAspNetCoreMvcUiBasicThemeModule),
    typeof(AbpAutofacModule),
    typeof(AbpAspNetCoreMultiTenancyModule),
    typeof(HolyBlessApplicationModule),
    typeof(HolyBlessEntityFrameworkCoreModule),
    typeof(AbpAccountWebOpenIddictModule),
    typeof(AbpSwashbuckleModule),
    typeof(AbpAspNetCoreSerilogModule),
    typeof(AbpBackgroundWorkersModule)
    )]
public class HolyBlessHttpApiHostModule : AbpModule
{
    public override void PreConfigureServices(ServiceConfigurationContext context)
    {
        var hostingEnvironment = context.Services.GetHostingEnvironment();
        var configuration = context.Services.GetConfiguration();
        var env = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Development";
        env = env.Replace("Development", "Dev")
                 .Replace("Production", "Prod");

        var appConfig = configuration.GetSection("AppConfigs").Get<AppConfig>() ?? new();
        appConfig.Environment = env;
        //context.Services.Configure<AppConfig>(configuration.GetSection("AppConfigs"));
        context.Services.AddSingleton(sp =>
        {
            return appConfig;
        });
        if (appConfig.ExposeWritableApi)
        {
            PreConfigure<OpenIddictBuilder>(builder =>
            {
                builder.AddValidation(options =>
                {
                    options.AddAudiences("HolyBless");
                    options.UseLocalServer();
                    options.UseAspNetCore();
                });
            });
        }
        if (!hostingEnvironment.IsDevelopment() && appConfig.ExposeWritableApi )
        {
            PreConfigure<AbpOpenIddictAspNetCoreOptions>(options =>
            {
                options.AddDevelopmentEncryptionAndSigningCertificate = false;
            });

            PreConfigure<OpenIddictServerBuilder>(serverBuilder =>
            {
                serverBuilder.AddProductionEncryptionAndSigningCertificate("openiddict.pfx", configuration["AuthServer:CertificatePassPhrase"]!);
                serverBuilder.SetIssuer(new Uri(configuration["AuthServer:Authority"]!));
            });
        }
        
    }

    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        var configuration = context.Services.GetConfiguration();
        var hostingEnvironment = context.Services.GetHostingEnvironment();

        //TODO: Temp disable authorization till permission management is implemented
        context.Services.AddAlwaysAllowAuthorization();

        // Register IHttpContextAccessor for accessing HTTP request headers in AppServices
        context.Services.AddHttpContextAccessor();

        if (!configuration.GetValue<bool>("App:DisablePII"))
        {
            Microsoft.IdentityModel.Logging.IdentityModelEventSource.ShowPII = true;
            Microsoft.IdentityModel.Logging.IdentityModelEventSource.LogCompleteSecurityArtifact = true;
        }
        var appConfig = configuration.GetSection("AppConfigs").Get<AppConfig>() ?? new();

        if (!configuration.GetValue<bool>("AuthServer:RequireHttpsMetadata") && appConfig.ExposeWritableApi)
        {
            Configure<OpenIddictServerAspNetCoreOptions>(options =>
            {
                options.DisableTransportSecurityRequirement = true;
            });

            Configure<ForwardedHeadersOptions>(options =>
            {
                options.ForwardedHeaders = ForwardedHeaders.XForwardedProto;
            });
            
        }
        Configure<AbpClockOptions>(options =>
        {
            options.Kind = DateTimeKind.Utc;
        });

        if (appConfig.ExposeWritableApi)
        {
            ConfigureAuthentication(context);
        }
        ConfigureUrls(configuration);
        ConfigureBundles();
        ConfigureConventionalControllers(appConfig.ExposeWritableApi);
        ConfigureSwagger(context, configuration, appConfig);
        ConfigureVirtualFileSystem(context);
        ConfigureCors(context, configuration);
        ConfigureBackgroundWorkers(appConfig);
        ConfigureLanguageCultures();
    }

    private void ConfigureLanguageCultures()
    {
        Configure<RequestLocalizationOptions>(options =>
        {
            var supportedCultures = new[]
            {
                "en",        // English
                "zh-Hans",   // Chinese (Simplified)
                "zh-Hant"    // Chinese (Traditional)
            };

            options.DefaultRequestCulture = new RequestCulture("zh-Hans");

            // Ensure SupportedCultures and SupportedUICultures are not null before using
            options.SupportedCultures ??= new List<CultureInfo>();
            options.SupportedUICultures ??= new List<CultureInfo>();

            options.SupportedCultures.Clear();
            options.SupportedUICultures.Clear();

            foreach (var culture in supportedCultures)
            {
                options.SupportedCultures.Add(new CultureInfo(culture));
                options.SupportedUICultures.Add(new CultureInfo(culture));
            }

            // Configure culture providers (order matters - first match wins)
            options.RequestCultureProviders.Clear();
            options.RequestCultureProviders.Add(new AcceptLanguageHeaderRequestCultureProvider());
            options.RequestCultureProviders.Add(new QueryStringRequestCultureProvider());
            options.RequestCultureProviders.Add(new CookieRequestCultureProvider());
        });
    }

    private static void ConfigureAuthentication(ServiceConfigurationContext context)
    {
        context.Services.ForwardIdentityAuthenticationForBearer(OpenIddictValidationAspNetCoreDefaults.AuthenticationScheme);
        context.Services.Configure<AbpClaimsPrincipalFactoryOptions>(options =>
        {
            options.IsDynamicClaimsEnabled = true;
        });
    }

    private void ConfigureUrls(IConfiguration configuration)
    {
        Configure<AppUrlOptions>(options =>
        {
            options.Applications["MVC"].RootUrl = configuration["App:SelfUrl"];
            options.Applications["Angular"].RootUrl = configuration["App:AngularUrl"];
            options.Applications["Angular"].Urls[AccountUrlNames.PasswordReset] = "account/reset-password";
            options.RedirectAllowedUrls.AddRange(configuration["App:RedirectAllowedUrls"]?.Split(',') ?? Array.Empty<string>());
        });
    }

    private void ConfigureBundles()
    {
        Configure<AbpBundlingOptions>(options =>
        {
            options.StyleBundles.Configure(
                BasicThemeBundles.Styles.Global,
                bundle =>
                {
                    bundle.AddFiles("/global-scripts.js");
                    bundle.AddFiles("/global-styles.css");
                }
            );
        });
    }

    private void ConfigureVirtualFileSystem(ServiceConfigurationContext context)
    {
        var hostingEnvironment = context.Services.GetHostingEnvironment();

        if (hostingEnvironment.IsDevelopment())
        {
            Configure<AbpVirtualFileSystemOptions>(options =>
            {
                options.FileSets.ReplaceEmbeddedByPhysical<HolyBlessDomainSharedModule>(Path.Combine(hostingEnvironment.ContentRootPath, $"..{Path.DirectorySeparatorChar}HolyBless.Domain.Shared"));
                options.FileSets.ReplaceEmbeddedByPhysical<HolyBlessDomainModule>(Path.Combine(hostingEnvironment.ContentRootPath, $"..{Path.DirectorySeparatorChar}HolyBless.Domain"));
                options.FileSets.ReplaceEmbeddedByPhysical<HolyBlessApplicationContractsModule>(Path.Combine(hostingEnvironment.ContentRootPath, $"..{Path.DirectorySeparatorChar}HolyBless.Application.Contracts"));
                options.FileSets.ReplaceEmbeddedByPhysical<HolyBlessApplicationModule>(Path.Combine(hostingEnvironment.ContentRootPath, $"..{Path.DirectorySeparatorChar}HolyBless.Application"));
            });
        }
    }

    private void ConfigureConventionalControllers(bool exposeWrittableApi)
    {
        Configure<AbpAspNetCoreMvcOptions>(options =>
        {
            if (exposeWrittableApi)
            {
                options.ConventionalControllers.Create(typeof(HolyBlessApplicationModule).Assembly,
                    controllerOptions =>
                    {
                        controllerOptions.ControllerModelConfigurer = model =>
                        {
                            if (model.ControllerType.Name.StartsWith("ReadOnly"))
                            {
                                model.ApiExplorer.IsVisible = false;
                            }
                        };
                    });
            }
            else
            {
                options.ConventionalControllers.Create(typeof(HolyBlessApplicationModule).Assembly,
                    controllerOptions =>
                    {
                        controllerOptions.ControllerModelConfigurer = model =>
                        {
                            if (!model.ControllerType.Name.StartsWith("ReadOnly"))
                            {
                                model.ApiExplorer.IsVisible = false;
                            }
                        };
                    });
                // Disable ABP module APIs
                options.ConventionalControllers.Create(typeof(AbpIdentityHttpApiModule).Assembly,
                    controllerOptions =>
                    {
                        controllerOptions.ControllerModelConfigurer = model =>
                        {
                            model.ApiExplorer.IsVisible = false;
                        };
                    });

                options.ConventionalControllers.Create(typeof(AbpAccountHttpApiModule).Assembly,
                    controllerOptions =>
                    {
                        controllerOptions.ControllerModelConfigurer = model =>
                        {
                            model.ApiExplorer.IsVisible = false;
                        };
                    });

                options.ConventionalControllers.Create(typeof(AbpSettingManagementHttpApiModule).Assembly,
                    controllerOptions =>
                    {
                        controllerOptions.ControllerModelConfigurer = model =>
                        {
                            model.ApiExplorer.IsVisible = false;
                        };
                    });

                //Can not disable all abp modules otherwise can not generate proxies for Angular
                /*
                options.ConventionalControllers.Create(typeof(AbpFeatureManagementHttpApiModule).Assembly,
                    controllerOptions =>
                    {
                        controllerOptions.ControllerModelConfigurer = model =>
                        {
                            model.ApiExplorer.IsVisible = false;
                        };
                    });
                */

                options.ConventionalControllers.Create(typeof(AbpPermissionManagementHttpApiModule).Assembly,
                    controllerOptions =>
                    {
                        controllerOptions.ControllerModelConfigurer = model =>
                        {
                            model.ApiExplorer.IsVisible = false;
                        };
                    });
            }
        });
    }

    private static void ConfigureSwagger(ServiceConfigurationContext context, IConfiguration configuration, AppConfig appConfig)
    {
        context.Services.AddAbpSwaggerGenWithOidc(
            configuration["AuthServer:Authority"]!,
            ["HolyBless"],
            [AbpSwaggerOidcFlows.AuthorizationCode],
            null,
            options =>
            {
                options.SwaggerDoc("v1", new OpenApiInfo { Title = "HolyBless API", Version = "v1" });
                options.DocInclusionPredicate((docName, description) => true);
                options.CustomSchemaIds(type => type.FullName);

                // Include XML comments
                var basePath = AppContext.BaseDirectory;

                options.IncludeXmlComments(Path.Combine(basePath, "HolyBless.Application.Contracts.xml"));
                options.IncludeXmlComments(Path.Combine(basePath, "HolyBless.Application.xml"));
                options.IncludeXmlComments(Path.Combine(basePath, "HolyBless.HttpApi.xml"));
                options.IncludeXmlComments(Path.Combine(basePath, "HolyBless.Domain.Shared.xml"));
                // Optional: show enums as strings
                options.SchemaGeneratorOptions = new SchemaGeneratorOptions
                {
                    UseAllOfForInheritance = false,
                    UseOneOfForPolymorphism = false,
                    UseInlineDefinitionsForEnums = true
                };

                //Hide ABP Controller for public site
                if (!appConfig.ExposeWritableApi)
                {
                    options.DocInclusionPredicate((docName, description) =>
                    {
                        var controllerName = description.ActionDescriptor.RouteValues["controller"];

                        // List of controllers to hide from Swagger
                        var controllersToHide = new[] { "Login", "Features" };

                        return !controllersToHide.Any(c =>
                            controllerName?.Contains(c, StringComparison.OrdinalIgnoreCase) == true
                            || (controllerName ?? "").StartsWith("Abp"));
                    });
                }
                //Allow Accept-Language header in Swagger UI
                options.OperationFilter<AcceptLanguageHeaderParameter>();
            });
    }

    // This filter adds Accept-Language header to all API endpoints in Swagger
    public class AcceptLanguageHeaderParameter : IOperationFilter
    {
        public void Apply(OpenApiOperation operation, OperationFilterContext context)
        {
            if (operation.Parameters == null)
                operation.Parameters = new List<OpenApiParameter>();

            operation.Parameters.Add(new OpenApiParameter
            {
                Name = "Accept-Language",
                In = ParameterLocation.Header,
                Required = false,
                Schema = new OpenApiSchema
                {
                    Type = "string",
                    // Optional: Predefine common languages
                    Default = new Microsoft.OpenApi.Any.OpenApiString("zh-Hans"),
                    Enum = new List<Microsoft.OpenApi.Any.IOpenApiAny>
                {
                    new Microsoft.OpenApi.Any.OpenApiString("en"),
                    new Microsoft.OpenApi.Any.OpenApiString("zh-Hans"),
                    new Microsoft.OpenApi.Any.OpenApiString("zh-Hant")
                }
                },
                Description = "Preferred reading language: en (English), zh-Hans (Simplified Chinese), zh-Hant (Traditional Chinese)"
            });

            // 2️⃣ x-user-language-audio header
            operation.Parameters.Add(new OpenApiParameter
            {
                Name = "X-User-Language-Audio",
                In = ParameterLocation.Header,
                Required = false,
                Schema = new OpenApiSchema
                {
                    Type = "string",
                    Enum = new List<Microsoft.OpenApi.Any.IOpenApiAny>
                {
                    new Microsoft.OpenApi.Any.OpenApiString("eng"),
                    new Microsoft.OpenApi.Any.OpenApiString("cmn"),
                    new Microsoft.OpenApi.Any.OpenApiString("yue")
                }
                },
                Description = "Preferred audio language: eng (English), cmn (Mandarin), yue (Cantonese)"
            });
        }
    }

    private static void ConfigureCors(ServiceConfigurationContext context, IConfiguration configuration)
    {
        context.Services.AddCors(options =>
        {
            options.AddDefaultPolicy(builder =>
            {
                builder
                    .WithOrigins(
                        configuration["App:CorsOrigins"]?
                            .Split(",", StringSplitOptions.RemoveEmptyEntries)
                            .Select(o => o.Trim().RemovePostFix("/"))
                            .ToArray() ?? Array.Empty<string>()
                    )
                    .WithAbpExposedHeaders()
                    .SetIsOriginAllowedToAllowWildcardSubdomains()
                    .AllowAnyHeader()
                    .AllowAnyMethod()
                    .AllowCredentials();
            });
        });
    }

    private void ConfigureBackgroundWorkers(AppConfig appConfig)
    {
        Configure<AbpBackgroundWorkerOptions>(options =>
        {
            options.IsEnabled = appConfig.EnableBackgroundWorkers;
        });
    }

    public override void OnApplicationInitialization(ApplicationInitializationContext context)
    {
        var app = context.GetApplicationBuilder();
        var env = context.GetEnvironment();

        app.UseForwardedHeaders();

        if (env.IsDevelopment())
        {
            app.UseDeveloperExceptionPage();
        }

        app.UseAbpRequestLocalization();

        if (!env.IsDevelopment())
        {
            app.UseErrorPage();
        }

        // This MUST be added before UseRouting()
        app.UseRequestLocalization();
        app.UseRouting();
        app.MapAbpStaticAssets();
        app.UseAbpStudioLink();
        app.UseAbpSecurityHeaders();
        app.UseCors();
        app.UseAuthentication();
        app.UseAbpOpenIddictValidation();

        if (MultiTenancyConsts.IsEnabled)
        {
            app.UseMultiTenancy();
        }

        app.UseUnitOfWork();
        app.UseDynamicClaims();
        app.UseAuthorization();

        app.UseSwagger();
        app.UseAbpSwaggerUI(options =>
        {
            options.SwaggerEndpoint("/swagger/v1/swagger.json", "HolyBless API");

            var configuration = context.ServiceProvider.GetRequiredService<IConfiguration>();
            options.OAuthClientId(configuration["AuthServer:SwaggerClientId"]);
        });
        app.UseAuditing();
        app.UseAbpSerilogEnrichers();
        app.UseConfiguredEndpoints();

        context.AddBackgroundWorkerAsync<BackgroundWorkers.StorageFileCheckWorker>();
    }

    public override async Task OnPostApplicationInitializationAsync(ApplicationInitializationContext context)
    {
        //Overwrite just disable UI
        using var scope = context.ServiceProvider.CreateScope();
        var settingManager = scope.ServiceProvider.GetRequiredService<ISettingManager>();
        await settingManager.SetGlobalAsync("Abp.Account.IsSelfRegistrationEnabled", "false");
    }
}